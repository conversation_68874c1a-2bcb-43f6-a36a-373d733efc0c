"use client";

import { ReactNode, useEffect, useState } from 'react';
import { AuthProvider } from '@/lib/auth';

interface ClientAuthProviderProps {
  children: ReactNode;
}

export default function ClientAuthProvider({ children }: ClientAuthProviderProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // During SSR or before hydration, render children without auth context
  if (!isClient) {
    return <>{children}</>;
  }

  // After hydration, render with auth context
  return (
    <AuthProvider>
      {children}
    </AuthProvider>
  );
}
