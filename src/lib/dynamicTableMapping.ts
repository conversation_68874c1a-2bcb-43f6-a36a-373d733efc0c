import { db } from './prisma';
import { DynamicTableMappingService, type TableMapping as DynamicTableMapping } from './dynamicTableMappingService';

/**
 * 数据库代码到模型映射的接口定义
 * @deprecated 使用 DynamicTableMappingService 中的 TableMapping
 */
export interface TableMapping {
  modelName: string;
  tableName: string;
  displayName: string;
  category: string;
  description: string;
}

/**
 * 硬编码的数据库代码到Prisma模型的映射配置
 * 作为备选方案，当数据库配置不可用时使用
 * @deprecated 现在优先使用数据库中的配置，这个映射仅作为备选
 * 只包含实际存在的数据库
 */
export const DATABASE_TABLE_MAPPING: Record<string, TableMapping> = {
  // 美国医疗器械数据库
  us_class: {
    modelName: 'uSClass',
    tableName: 'us_class',
    displayName: 'US Classification',
    category: 'Regular',
    description: 'US Medical Device Classification Database'
  },

  us_pmn: {
    modelName: 'uSPremarketNotification',
    tableName: 'us_pmn',
    displayName: 'US Premarket Notification',
    category: 'Marketed',
    description: 'US FDA Premarket Notification (510k) Database'
  },


};

/**
 * 获取数据库映射配置
 * 优先从数据库获取，失败时使用硬编码备选
 * @param databaseCode 数据库代码
 * @returns 映射配置对象
 */
export async function getTableMapping(databaseCode: string): Promise<TableMapping | null> {
  try {
    // 优先使用新的动态服务
    const dynamicMapping = await DynamicTableMappingService.getTableMapping(databaseCode);
    if (dynamicMapping) {
      return dynamicMapping;
    }
  } catch (error) {
    console.warn(`[getTableMapping] 从数据库获取配置失败，使用备选方案: ${error}`);
  }

  // 备选：使用硬编码映射
  const fallbackMapping = DATABASE_TABLE_MAPPING[databaseCode];
  if (fallbackMapping) {
    console.warn(`[getTableMapping] 使用硬编码备选配置: ${databaseCode}`);
    return fallbackMapping;
  }

  return null;
}

/**
 * 动态获取Prisma模型实例
 * 这个函数替代了之前在API路由中硬编码的模型获取逻辑
 * @param databaseCode 数据库代码
 * @returns Prisma模型实例
 */
export async function getDynamicModel(databaseCode: string) {
  try {
    // 优先使用新的动态服务
    return await DynamicTableMappingService.getDynamicModel(databaseCode);
  } catch (error) {
    console.warn(`[getDynamicModel] 动态服务失败，尝试备选方案: ${error}`);
    
    // 备选：使用硬编码映射
    const mapping = DATABASE_TABLE_MAPPING[databaseCode];
    if (!mapping) {
      throw new Error(`不支持的数据库代码: ${databaseCode}. 支持的代码: ${Object.keys(DATABASE_TABLE_MAPPING).join(', ')}`);
    }

    // 通过动态属性访问获取对应的Prisma模型
    const model = (db as any)[mapping.modelName];
    if (!model) {
      throw new Error(`找不到模型: ${mapping.modelName}`);
    }

    return model;
  }
}

/**
 * 验证数据库代码是否有效
 * @param databaseCode 数据库代码
 * @returns 验证结果
 */
export async function validateDatabaseCode(databaseCode: string): Promise<{ isValid: boolean; error?: string; status?: number }> {
  try {
    // 优先使用新的动态服务
    return await DynamicTableMappingService.validateDatabaseCode(databaseCode);
  } catch (error) {
    console.warn(`[validateDatabaseCode] 动态服务失败，使用备选验证: ${error}`);
    
    // 备选：检查硬编码映射
    if (!databaseCode || typeof databaseCode !== 'string') {
      return {
        isValid: false,
        error: '数据库代码不能为空',
        status: 400
      };
    }

    if (DATABASE_TABLE_MAPPING[databaseCode]) {
      return { isValid: true };
    }

    return {
      isValid: false,
      error: `不支持的数据库代码: ${databaseCode}`,
      status: 404
    };
  }
}

/**
 * 获取所有可用的数据库代码
 * @returns 数据库代码数组
 */
export async function getAllDatabaseCodes(): Promise<string[]> {
  try {
    // 优先使用新的动态服务
    const dynamicCodes = await DynamicTableMappingService.getAllDatabaseCodes();
    if (dynamicCodes.length > 0) {
      return dynamicCodes;
    }
  } catch (error) {
    console.warn(`[getAllDatabaseCodes] 动态服务失败，使用备选列表: ${error}`);
  }

  // 备选：返回硬编码的数据库代码
  return Object.keys(DATABASE_TABLE_MAPPING);
}

/**
 * 检查Prisma模型是否有效
 * @param model Prisma模型实例
 * @returns 是否为有效的Prisma模型
 */
export function isPrismaModel(model: unknown): model is { findMany: (args: unknown) => unknown; groupBy: (args: unknown) => unknown } {
  return DynamicTableMappingService.isPrismaModel(model);
}

/**
 * 获取数据库访问权限级别
 * 这个函数用于权限控制，替代硬编码的访问级别映射
 * @param databaseCode 数据库代码
 * @returns 访问权限级别
 */
export function getDatabaseAccessLevel(databaseCode: string): string {
  // 根据实际存在的数据库配置访问级别
  const freeDatabase = ['us_class', 'us_pmn']; // 实际存在的免费数据库

  if (freeDatabase.includes(databaseCode)) return 'free';

  return 'premium'; // 默认需要高级权限
}

/**
 * 为分析和日志功能提供的辅助函数
 * 从路径中提取数据库代码
 * @param path URL路径
 * @returns 提取的数据库代码，如果无法提取则返回null
 */
export async function extractDatabaseCodeFromPath(path: string): Promise<string | null> {
  // 匹配 /data/list/[database] 或 /api/data/[database] 等模式
  const matches = path.match(/\/(?:data\/list|api\/data|api\/meta|api\/stats|api\/export|api\/advanced-search)\/([^\/\?]+)/);
  if (matches && matches[1]) {
    const validation = await validateDatabaseCode(matches[1]);
    if (validation.isValid) {
      return matches[1];
    }
  }
  return null;
}

// 向后兼容的导出
export { DynamicTableMappingService }; 